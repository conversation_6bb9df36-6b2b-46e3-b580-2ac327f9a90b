<?php

namespace Theme\Gofunz\Http\Controllers;

use <PERSON><PERSON>ble\Blog\Models\Post;
use <PERSON><PERSON>ble\Blog\Repositories\Interfaces\CategoryInterface;
use Bo<PERSON>ble\Blog\Repositories\Interfaces\PostInterface;
use <PERSON><PERSON><PERSON>\BookingPlace\Models\BookingPlace;
use <PERSON><PERSON><PERSON>\Slug\Models\Slug;
use Botble\Theme\Facades\Theme;
use Botble\Theme\Http\Controllers\PublicController;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Botble\Member\Models\Member;
use Illuminate\Support\Facades\Auth;

class GofunzController extends PublicController
{
    public function getIndex()
    {
        // Get coordinates from cookies - use $_COOKIE directly since these are unencrypted cookies set by JavaScript
        $inputLat = isset($_COOKIE['user_latitude']) ? (float) $_COOKIE['user_latitude'] : null;
        $inputLng = isset($_COOKIE['user_longitude']) ? (float) $_COOKIE['user_longitude'] : null;

        // Check if coordinates are valid
        $hasValidCoordinates = $inputLat !== null && $inputLng !== null
            && $inputLat >= -90 && $inputLat <= 90
            && $inputLng >= -180 && $inputLng <= 180;

        if ($hasValidCoordinates) {
            try {
                // Use geospatial filtering when coordinates are available
                $radiusKm = 25; // Default 25km radius for home page
                $box = $this->calculateBoundingBox($inputLat, $inputLng, $radiusKm);

                // Get nearby places using geospatial filtering
                $places = BookingPlace::wherePublished()
                    ->withinGeographicRadius($box, $inputLat, $inputLng, $radiusKm)
                    ->orderBy('distance_meters', 'asc')
                    ->limit(12)
                    ->get();

                // Add formatted distance to each place
                $places->each(function ($place) {
                    $distanceKm = round($place->distance_meters / 1000, 2);
                    $place->distance_km = $distanceKm;
                    $place->distance_formatted = $distanceKm < 1
                        ? round($place->distance_meters) . 'm'
                        : $distanceKm . 'km';
                });

            } catch (\Exception $e) {
                // Log error and fall back to latest places
                Log::error('Error in getIndex geospatial filtering: ' . $e->getMessage(), [
                    'latitude' => $inputLat,
                    'longitude' => $inputLng,
                    'coordinate_source' => 'cookie'
                ]);
                $hasValidCoordinates = false;
            }
        }

        // Fallback to original logic when coordinates are not available or invalid
        if (!$hasValidCoordinates) {
            $places = get_latest_places(6, [], 0);
        }

        $categories = get_booking_categories(8);
        $posts = get_featured_posts(8, [
            'author',
            'tags'
        ]);
        $blog_categories = get_categories();

        return Theme::scope('home', compact('places', 'categories', 'posts', 'blog_categories'))->render();
    }

    public function getSearch(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'category' => 'nullable|string',
            'page' => 'integer|min:1',
            'radius' => 'nullable|numeric|min:1|max:100', // Optional radius in km, default 25km
            'datetime' => 'nullable|string',
            'location' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return Theme::scope('404')->render();
        }
        $categoryId = $request->input('category');
        $datetime = $request->input('datetime');
        $location = $request->input('q');

        $page = $request->input('page', 1);
        $perPage = 8; // Items per page
        $offset = ($page - 1) * $perPage;
        $radiusKm = (float) $request->input('radius', 25); // Default 25km

        // Get coordinates from cookies - use $_COOKIE directly since these are unencrypted cookies set by JavaScript
        $inputLat = isset($_COOKIE['user_latitude']) ? (float) $_COOKIE['user_latitude'] : null;
        $inputLng = isset($_COOKIE['user_longitude']) ? (float) $_COOKIE['user_longitude'] : null;

        // Check if coordinates are valid
        $hasValidCoordinates = $inputLat !== null && $inputLng !== null
            && $inputLat >= -90 && $inputLat <= 90
            && $inputLng >= -180 && $inputLng <= 180;

        if ($hasValidCoordinates) {
            try {
                // Use geospatial filtering when coordinates are available
                $box = $this->calculateBoundingBox($inputLat, $inputLng, $radiusKm);

                // Build base query with geospatial filtering
                $query = BookingPlace::wherePublished()
                    ->applyQuerySearch($request->all())
                    ->withinGeographicRadius($box, $inputLat, $inputLng, $radiusKm);

                // Get total count for pagination
                $totalPlaces = $query->count();

                // Get places with pagination
                $places = $query->orderBy('distance_meters', 'asc')
                    ->offset($offset)
                    ->limit($perPage)
                    ->get();

                // Add formatted distance to each place
                $places->each(function ($place) {
                    $distanceKm = round($place->distance_meters / 1000, 2);
                    $place->distance_km = $distanceKm;
                    $place->distance_formatted = $distanceKm < 1
                        ? round($place->distance_meters) . 'm'
                        : $distanceKm . 'km';
                });

            } catch (\Exception $e) {
                // Log error and fall back to non-geospatial queries
                Log::error('Error in getSearch geospatial filtering: ' . $e->getMessage(), [
                    'latitude' => $inputLat,
                    'longitude' => $inputLng,
                    'radius' => $radiusKm,
                    'category' => $categoryId
                ]);

                $hasValidCoordinates = false;
            }
        }

        // Fallback to original logic when coordinates are not available or invalid
        if (!$hasValidCoordinates) {
            if ($categoryId) {
                $places = get_places_by_category($categoryId, $perPage, [], $offset);
                $totalPlaces = count_places_by_category($categoryId);
            } else {
                $places = get_latest_places($perPage, [], $offset);
                $totalPlaces = count_latest_places();
            }
        }

        // Calculate pagination
        $totalPages = ceil($totalPlaces / $perPage);
        $pagination = [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'per_page' => $perPage,
            'total_items' => $totalPlaces,
            'has_prev' => $page > 1,
            'has_next' => $page < $totalPages,
            'prev_page' => $page > 1 ? $page - 1 : null,
            'next_page' => $page < $totalPages ? $page + 1 : null,
        ];

        $categories = get_booking_categories(8);
        return Theme::scope('search', compact('places', 'categories', 'pagination'))->render();
    }

    public function getMoreSearch(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'category' => 'required|string',
            'offset' => 'integer|min:0',
            'radius' => 'nullable|numeric|min:1|max:100' // Optional radius in km, default 25km
        ]);

        if ($validator->fails()) {
            return '';
        }

        // return partial view 'list-place-card'
        $categoryId = $request->input('category');
        $offset = $request->input('offset', 0);
        $radiusKm = (float) $request->input('radius', 25); // Default 25km
        $limit = 8;

        // Get coordinates from cookies - use $_COOKIE directly since these are unencrypted cookies set by JavaScript
        $inputLat = isset($_COOKIE['user_latitude']) ? (float) $_COOKIE['user_latitude'] : null;
        $inputLng = isset($_COOKIE['user_longitude']) ? (float) $_COOKIE['user_longitude'] : null;

        // Check if coordinates are valid
        $hasValidCoordinates = $inputLat !== null && $inputLng !== null
            && $inputLat >= -90 && $inputLat <= 90
            && $inputLng >= -180 && $inputLng <= 180;

        if ($hasValidCoordinates) {
            try {
                // Use geospatial filtering when coordinates are available
                $box = $this->calculateBoundingBox($inputLat, $inputLng, $radiusKm);

                // Build query with geospatial filtering and category filtering
                $places = BookingPlace::wherePublished()
                    ->withinGeographicRadius($box, $inputLat, $inputLng, $radiusKm)
                    ->whereHas('categories', function ($categoryQuery) use ($categoryId) {
                        $categoryQuery->where('categories.id', $categoryId);
                    })
                    ->orderBy('distance_meters', 'asc')
                    ->offset($offset)
                    ->limit($limit)
                    ->get();

                // Add formatted distance to each place
                $places->each(function ($place) {
                    $distanceKm = round($place->distance_meters / 1000, 2);
                    $place->distance_km = $distanceKm;
                    $place->distance_formatted = $distanceKm < 1
                        ? round($place->distance_meters) . 'm'
                        : $distanceKm . 'km';
                });

            } catch (\Exception $e) {
                // Log error and fall back to non-geospatial query
                Log::error('Error in getMoreSearch geospatial filtering: ' . $e->getMessage(), [
                    'latitude' => $inputLat,
                    'longitude' => $inputLng,
                    'radius' => $radiusKm,
                    'category' => $categoryId
                ]);
                $hasValidCoordinates = false;
            }
        }

        // Fallback to original logic when coordinates are not available or invalid
        if (!$hasValidCoordinates) {
            $places = get_places_by_category($categoryId, $limit, [], $offset);
        }

        return Theme::partial('components.list-place-card', compact('places'));
    }

    public function getView(?string $key = null, string $prefix = '')
    {
        return parent::getView($key);
    }

    public function getSiteMapIndex(string $key = null, string $extension = 'xml')
    {
        return parent::getSiteMapIndex();
    }

    // AJAX: getListPlace
    public function getListPlace(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'limit' => 'required|integer|min:1|max:20',
            'offset' => 'required|integer|min:1'
        ]);

        if ($validate->fails()) {
            return response()->json($validate->errors(), 422);
        }

        $offset = $request->input('offset');
        $limit = $request->input('limit');

        // List place logic here
        $places = get_latest_places($limit, [], $offset);

        // load view from themes/gofunz/partials/components/place-card.blade.php
        return Theme::partial('components.place-card', compact('places'));
    }

    // route: getListBlog
    public function getListBlog(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1|max:20',
            'category' => 'string|nullable'
        ]);
        if ($validator->fails()) {
            return Theme::scope('404')->render();
        }
        $page = $request->input('page', 1);
        $limit = $request->input('limit', 8);
        $categoryId = $request->input('category', '');
        $query = app(PostInterface::class)
            ->getModel()
            ->wherePublished()
            ->whereHas('categories', function (Builder $query) use ($categoryId): void {
                if ($categoryId) {
                    $query->whereIn('categories.id', array_filter((array) $categoryId));
                }
            })
            ->select('*')
            ->distinct()
            ->with('slugable')
            ->orderByDesc('created_at');
        $totalPosts = $query->count();
        $posts = $query->limit($limit)
            ->offset(($page - 1) * $limit)
            ->get();
        $blog_categories = get_categories();
        $pagination = $this->buildPagination($page, ceil($totalPosts / $limit));

        return Theme::scope('blog.list', compact('posts', 'blog_categories', 'pagination'))->render();
    }

    public function getPlace(Request $request, string $slug)
    {
        $place = BookingPlace::whereSlug($slug)
            ->with(['services', 'posts'])
            ->firstOrFail();
        return Theme::scope('place', compact('place'))->render();
    }

    public function getDetailBlog(Request $request, string $slug)
    {
        // Find the slug record first
        $slugRecord = Slug::query()
            ->where('key', $slug)
            ->where('reference_type', Post::class)
            ->first();

        // If slug doesn't exist, return 404
        if (!$slugRecord) {
            abort(404);
        }

        // Get the blog post using the slug's reference_id
        $post = app(PostInterface::class)
            ->getModel()
            ->wherePublished()
            ->where('id', $slugRecord->reference_id)
            ->with(['categories', 'tags', 'slugable', 'categories.slugable', 'tags.slugable'])
            ->firstOrFail();

        // Get related posts based on categories and tags
        $relatedPosts = $this->getRelatedPosts($post, 4);

        return Theme::scope('blog.detail', compact('post', 'relatedPosts'))->render();
    }

    public function listPriorityPlaces(Request $request)
    {
        // Get coordinates from cookies - use $_COOKIE directly since these are unencrypted cookies set by JavaScript
        $inputLat = isset($_COOKIE['user_latitude']) ? (float) $_COOKIE['user_latitude'] : null;
        $inputLng = isset($_COOKIE['user_longitude']) ? (float) $_COOKIE['user_longitude'] : null;

        $coordinates = [$inputLat, $inputLng];

        // Validate coordinates
        if ($inputLat < -90 || $inputLat > 90 || $inputLng < -180 || $inputLng > 180) {
            // Invalid coordinates, show newest places
            $places = BookingPlace::wherePublished()
                ->orderByDesc('created_at')
                ->limit(20)
                ->get();

            return Theme::scope('map', compact('places', 'coordinates'))->render();
        }

        // Validate optional request parameters
        $validator = Validator::make($request->all(), [
            'radius' => 'nullable|numeric|min:1|max:100', // Optional radius in km, default 25km
            'limit' => 'nullable|integer|min:1|max:50', // Optional limit, default 24
        ]);

        if ($validator->fails()) {
            $places = BookingPlace::wherePublished()
                    ->orderByDesc('created_at')
                    ->limit(20)
                    ->get();

            return Theme::scope('map', compact('places', 'coordinates'))->render();
        }
        $radiusKm = (float) $request->input('radius', 25); // Default 25km
        $limit = (int) $request->input('limit', 24); // Default 24 places

        try {

            $box = $this->calculateBoundingBox($inputLat, $inputLng, $radiusKm);

            // Query places within the bounding box and calculate exact distances using the reusable scope
            // Note: coordinates in DB are stored as "latitude,longitude" but ST_GeomFromText expects "longitude,latitude"
            $places = BookingPlace::wherePublished()
                ->with(['categories'])
                ->withinGeographicRadius($box, $inputLat, $inputLng, $radiusKm)
                ->orderBy('distance_meters', 'asc')
                ->limit($limit)
                ->get();

            // Add formatted distance to each place
            $places->each(function ($place) {
                $distanceKm = round($place->distance_meters / 1000, 2);
                $place->distance_km = $distanceKm;
                $place->distance_formatted = $distanceKm < 1
                    ? round($place->distance_meters) . 'm'
                    : $distanceKm . 'km';
            });

            return Theme::scope('map', compact('places', 'coordinates'))->render();

        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Error in listPriorityPlaces: ' . $e->getMessage(), [
                'latitude' => $inputLat ?? null,
                'longitude' => $inputLng ?? null,
                'radius' => $radiusKm ?? null,
                'coordinate_source' => 'cookie'
            ]);

            // Return map view with newest places on error
            $places = BookingPlace::wherePublished()
                ->orderByDesc('created_at')
                ->limit(24)
                ->get();

            return Theme::scope('map', compact('places', 'coordinates'))->render();
        }
    }

    private function buildPagination($currentPage, $totalPages)
    {
        return [
            'current_page' => $currentPage,
            'total_pages' => $totalPages,
            'has_prev' => $currentPage > 1,
            'has_next' => $currentPage < $totalPages,
            'prev_page' => $currentPage > 1 ? $currentPage - 1 : null,
            'next_page' => $currentPage < $totalPages ? $currentPage + 1 : null,
        ];
    }

    /**
     * Get related posts based on categories and tags
     */
    private function getRelatedPosts($currentPost, $limit = 4)
    {
        $categoryIds = $currentPost->categories->pluck('id')->toArray();
        $tagIds = $currentPost->tags->pluck('id')->toArray();

        $query = app(PostInterface::class)
            ->getModel()
            ->wherePublished()
            ->where('id', '!=', $currentPost->id) // Exclude current post
            ->with(['categories', 'tags', 'slugable']);

        // If post has categories or tags, prioritize posts with same categories/tags
        if (!empty($categoryIds) || !empty($tagIds)) {
            $query->where(function ($q) use ($categoryIds, $tagIds) {
                if (!empty($categoryIds)) {
                    $q->whereHas('categories', function ($categoryQuery) use ($categoryIds) {
                        $categoryQuery->whereIn('categories.id', $categoryIds);
                    });
                }

                if (!empty($tagIds)) {
                    $q->orWhereHas('tags', function ($tagQuery) use ($tagIds) {
                        $tagQuery->whereIn('tags.id', $tagIds);
                    });
                }
            });
        }

        $relatedPosts = $query->orderByDesc('created_at')
            ->limit($limit)
            ->get();

        // If we don't have enough related posts, fill with latest posts
        if ($relatedPosts->count() < $limit) {
            $remainingLimit = $limit - $relatedPosts->count();
            $excludeIds = $relatedPosts->pluck('id')->push($currentPost->id)->toArray();

            $latestPosts = app(PostInterface::class)
                ->getModel()
                ->wherePublished()
                ->whereNotIn('id', $excludeIds)
                ->with(['categories', 'tags', 'slugable'])
                ->orderByDesc('created_at')
                ->limit($remainingLimit)
                ->get();

            $relatedPosts = $relatedPosts->merge($latestPosts);
        }

        return $relatedPosts;
    }

    /**
     * Calculate bounding box for geospatial queries
     *
     * @param float $lat Latitude
     * @param float $lng Longitude
     * @param float $radiusKm Radius in kilometers
     * @return array Bounding box with minLat, maxLat, minLng, maxLng
     */
    private function calculateBoundingBox(float $lat, float $lng, float $radiusKm): array
    {
        $earthRadius = 6371; // Earth radius in kilometers

        $latRange = rad2deg($radiusKm / $earthRadius);
        $lngRange = rad2deg($radiusKm / ($earthRadius * cos(deg2rad($lat))));

        return [
            'minLat' => $lat - $latRange,
            'maxLat' => $lat + $latRange,
            'minLng' => $lng - $lngRange,
            'maxLng' => $lng + $lngRange
        ];
    }

    /**
     * Get address search suggestions with user location radius filtering
     * API endpoint for AJAX address search suggestions only
     */
    public function ajaxSearch(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'q' => ['required', 'string', 'min:2', 'max:255'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $q = $request->get('q');
            $radiusKm = 25;

            // Get coordinates from cookies
            $userLat = isset($_COOKIE['user_latitude']) ? (float) $_COOKIE['user_latitude'] : null;
            $userLng = isset($_COOKIE['user_longitude']) ? (float) $_COOKIE['user_longitude'] : null;

            // Check if coordinates are valid
            $hasValidCoordinates = $userLat !== null && $userLng !== null
                && $userLat >= -90 && $userLat <= 90
                && $userLng >= -180 && $userLng <= 180;

            // Address search query only
            $placesQuery = BookingPlace::wherePublished()
                ->where(function ($query) use ($q) {
                    $query->where('name', 'LIKE', "%{$q}%")
                        ->orWhere('address', 'LIKE', "%{$q}%")
                        ->orWhere('address_2', 'LIKE', "%{$q}%")
                        ->orWhere('city', 'LIKE', "%{$q}%");
                });

            // Apply radius filtering if user location is provided
            if ($hasValidCoordinates) {
                try {
                    $box = $this->calculateBoundingBox($userLat, $userLng, $radiusKm);
                    $places = $placesQuery->withinGeographicRadius($box, $userLat, $userLng, $radiusKm)
                        ->orderBy('distance_meters', 'asc')
                        ->limit(5)
                        ->get();

                    // Add formatted distance to each place
                    $places->each(function ($place) {
                        $distanceKm = round($place->distance_meters / 1000, 1);
                        $place->distance_km = $distanceKm;
                        $place->distance_formatted = $distanceKm < 1
                            ? round($place->distance_meters) . 'm'
                            : $distanceKm . 'km';
                    });
                } catch (\Exception $e) {
                    // Log error and fall back to non-geospatial query
                    Log::error('Error in ajaxSearch geospatial filtering: ' . $e->getMessage(), [
                        'latitude' => $userLat,
                        'longitude' => $userLng,
                        'radius' => $radiusKm
                    ]);
                    $hasValidCoordinates = false;
                }
            }

            // Fallback to basic search when coordinates are not available or invalid
            if (!$hasValidCoordinates) {
                $places = $placesQuery
                    ->select('id', 'name', 'address', 'address_2', 'city', 'coordinates')
                    ->orderBy('name', 'asc')
                    ->limit(5)
                    ->get();
            }

            // Format results
            $suggestions = $places->map(function ($place) use ($userLat, $userLng, $hasValidCoordinates) {
                $result = [
                    'id' => $place->id,
                    'name' => $place->name,
                    'address' => trim($place->address . ' ' . $place->address_2),
                    'city' => $place->city,
                    'full_address' => trim($place->address . ' ' . $place->address_2 . ', ' . $place->city),
                ];

                // Add distance if user location provided
                if ($hasValidCoordinates) {
                    if (isset($place->distance_km) && isset($place->distance_formatted)) {
                        // Use pre-calculated distance from geospatial query
                        $result['distance_km'] = $place->distance_km;
                        $result['distance_formatted'] = $place->distance_formatted;
                    } elseif ($place->coordinates) {
                        // Fallback calculation using helper function
                        [$lat, $lng] = explode(',', $place->coordinates);
                        $distanceKm = distance($userLat, $userLng, (float)$lat, (float)$lng);
                        $result['distance_km'] = round($distanceKm, 1);
                        $result['distance_formatted'] = $distanceKm < 1
                            ? round($distanceKm * 1000) . 'm'
                            : round($distanceKm, 1) . 'km';
                    }
                }

                return $result;
            });

            return response()->json([
                'success' => true,
                'data' => $suggestions,
                'total' => $suggestions->count(),
                'query' => $q,
                'radius_km' => $radiusKm
            ]);

        } catch (\Exception $e) {
            Log::error('Place search suggestions failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tìm kiếm. Vui lòng thử lại sau.'
            ], 500);
        }
    }

    /**
     * Get category search suggestions
     * API endpoint for AJAX category search suggestions
     */
    public function categoryAjaxSearch(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'q' => ['required', 'string', 'min:2', 'max:255'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $q = $request->get('q');

            // Search for categories by name and return id and name
            $categories = \Botble\BookingPlace\Models\BookingCategory::where('name', 'LIKE', "%{$q}%")
                ->select('id', 'name')
                ->distinct()
                ->orderBy('name', 'asc')
                ->limit(10)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $categories,
                'total' => count($categories),
                'query' => $q,
                'search_type' => 'category'
            ]);

        } catch (\Exception $e) {
            Log::error('Category search suggestions failed: ' . $e->getMessage(), [
                'query' => $request->get('q'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tìm kiếm danh mục. Vui lòng thử lại sau.'
            ], 500);
        }
    }

    /**
     * Custom member settings page with our theme design
     */
    public function getProfile()
    {
        /**
         * @var \Botble\Member\Models\Member $user
         */
        $user = auth('member')->user();

        if (!$user) {
            return redirect()->route('public.index');
        }

        \Botble\Base\Facades\Assets::addScripts('form-validation');

        $profileForm = \Botble\Member\Forms\Fronts\ProfileForm::createFromModel($user)->renderForm();
        $changePasswordForm = \Botble\Member\Forms\Fronts\ChangePasswordForm::create()->renderForm();

        return Theme::scope('profile', compact('user', 'profileForm', 'changePasswordForm'))->render();
    }

    public function getWishlist()
    {
        $member = auth('member')->user();

        if (!$member) {
            return redirect()->route('public.index');
        }

        // Get coordinates from cookies - use $_COOKIE directly since these are unencrypted cookies set by JavaScript
        $inputLat = isset($_COOKIE['user_latitude']) ? (float) $_COOKIE['user_latitude'] : null;
        $inputLng = isset($_COOKIE['user_longitude']) ? (float) $_COOKIE['user_longitude'] : null;

        // Check if coordinates are valid
        $hasValidCoordinates = $inputLat !== null && $inputLng !== null
            && $inputLat >= -90 && $inputLat <= 90
            && $inputLng >= -180 && $inputLng <= 180;

        if ($hasValidCoordinates) {
            try {
                // Use geospatial filtering when coordinates are available
                $radiusKm = 100; // Larger radius for wishlist to include all saved places
                $box = $this->calculateBoundingBox($inputLat, $inputLng, $radiusKm);

                // Get wishlist places with distance calculation using a custom approach
                // to avoid the duplicate column issue with belongsToMany relationships
                $places = BookingPlace::wherePublished()
                    ->whereIn('id', function($query) use ($member) {
                        $query->select('place_id')
                              ->from('booking_place_wishlists')
                              ->where('member_id', $member->id);
                    })
                    ->with(['categories', 'services'])
                    ->withinGeographicRadius($box, $inputLat, $inputLng, $radiusKm)
                    ->orderBy('distance_meters', 'asc')
                    ->paginate(24);

                // Add formatted distance to each place
                $places->getCollection()->each(function ($place) {
                    $distanceKm = round($place->distance_meters / 1000, 2);
                    $place->distance_km = $distanceKm;
                    $place->distance_formatted = $distanceKm < 1
                        ? round($place->distance_meters) . 'm'
                        : $distanceKm . 'km';
                });

            } catch (\Exception $e) {
                // Log error and fall back to default wishlist without distance
                Log::error('Error in getWishlist geospatial filtering: ' . $e->getMessage(), [
                    'latitude' => $inputLat,
                    'longitude' => $inputLng,
                    'member_id' => $member->id
                ]);
                $hasValidCoordinates = false;
            }
        }

        // Fallback to original logic when coordinates are not available or invalid
        if (!$hasValidCoordinates) {
            $places = $member->wishlistPlaces()
                ->with(['categories', 'services'])
                ->paginate(24);
        }

        return Theme::scope('wishlist', compact('places'))->render();
    }
}

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth('member')->user();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Người dùng chưa đăng nhập.'
                ], 401);
            }

            // Split name into first_name and last_name using last space as delimiter
            $name = trim($request->input('name'));
            $nameParts = explode(' ', $name);
            
            if (count($nameParts) > 1) {
                $lastName = array_pop($nameParts);
                $firstName = implode(' ', $nameParts);
            } else {
                $firstName = $name;
                $lastName = '';
            }

            // Update user profile
            $user->update([
                'first_name' => $firstName,
                'last_name' => $lastName,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Cập nhật thông tin thành công.',
                'data' => [
                    'name' => $user->name,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Profile update failed: ' . $e->getMessage(), [
                'user_id' => auth('member')->id(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật thông tin. Vui lòng thử lại sau.'
            ], 500);
        }
    }

    /**
     * Delete user account
     */
    public function deleteAccount(Request $request)
    {
        try {
            $user = auth('member')->user();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Người dùng chưa đăng nhập.'
                ], 401);
            }

            // Log the account deletion
            Log::info('User account deleted', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_name' => $user->name,
            ]);

            // Delete the user account
            $user->delete();

            // Logout the user
            auth('member')->logout();

            return response()->json([
                'success' => true,
                'message' => 'Tài khoản đã được xóa thành công.'
            ]);

        } catch (\Exception $e) {
            Log::error('Account deletion failed: ' . $e->getMessage(), [
                'user_id' => auth('member')->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa tài khoản. Vui lòng thử lại sau.'
            ], 500);
        }
    }
