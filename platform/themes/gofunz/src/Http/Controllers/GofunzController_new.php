<?php

namespace Theme\Gofunz\Http\Controllers;

use <PERSON><PERSON>ble\Blog\Models\Post;
use <PERSON><PERSON>ble\Blog\Repositories\Interfaces\CategoryInterface;
use Bo<PERSON>ble\Blog\Repositories\Interfaces\PostInterface;
use <PERSON><PERSON><PERSON>\BookingPlace\Models\BookingPlace;
use <PERSON><PERSON><PERSON>\Member\Models\Member;
use Bo<PERSON>ble\Slug\Models\Slug;
use Bo<PERSON>ble\Theme\Facades\Theme;
use Botble\Theme\Http\Controllers\PublicController;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class GofunzController extends PublicController
{
    public function getIndex()
    {
        // Get coordinates from cookies - use $_COOKIE directly since these are unencrypted cookies set by JavaScript
        $inputLat = isset($_COOKIE['user_latitude']) ? (float) $_COOKIE['user_latitude'] : null;
        $inputLng = isset($_COOKIE['user_longitude']) ? (float) $_COOKIE['user_longitude'] : null;

        // Check if coordinates are valid
        $hasValidCoordinates = $inputLat !== null && $inputLng !== null
            && $inputLat >= -90 && $inputLat <= 90
            && $inputLng >= -180 && $inputLng <= 180;

        if ($hasValidCoordinates) {
            try {
                // Use geospatial filtering when coordinates are available
                $radiusKm = 25; // Default 25km radius for home page
                $box = $this->calculateBoundingBox($inputLat, $inputLng, $radiusKm);

                // Get nearby places using geospatial filtering
                $places = BookingPlace::wherePublished()
                    ->withinGeographicRadius($box, $inputLat, $inputLng, $radiusKm)
                    ->orderBy('distance_meters', 'asc')
                    ->limit(12)
                    ->get();

                // Add formatted distance to each place
                $places->each(function ($place) {
                    $distanceKm = round($place->distance_meters / 1000, 2);
                    $place->distance_km = $distanceKm;
                    $place->distance_formatted = $distanceKm < 1
                        ? round($place->distance_meters) . 'm'
                        : $distanceKm . 'km';
                });

            } catch (\Exception $e) {
                // Log error and fall back to latest places
                Log::error('Error in getIndex geospatial filtering: ' . $e->getMessage(), [
                    'latitude' => $inputLat,
                    'longitude' => $inputLng,
                    'coordinate_source' => 'cookie'
                ]);
                $hasValidCoordinates = false;
            }
        }

        // Fallback to original logic when coordinates are not available or invalid
        if (!$hasValidCoordinates) {
            $places = get_latest_places(6, [], 0);
        }

        $categories = get_booking_categories(8);
        $posts = get_featured_posts(8, [
            'author',
            'tags'
        ]);
        $blog_categories = get_categories();

        return Theme::scope('home', compact('places', 'categories', 'posts', 'blog_categories'))->render();
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth('member')->user();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Người dùng chưa đăng nhập.'
                ], 401);
            }

            // Split name into first_name and last_name using last space as delimiter
            $name = trim($request->input('name'));
            $nameParts = explode(' ', $name);
            
            if (count($nameParts) > 1) {
                $lastName = array_pop($nameParts);
                $firstName = implode(' ', $nameParts);
            } else {
                $firstName = $name;
                $lastName = '';
            }

            // Update user profile
            $user->update([
                'first_name' => $firstName,
                'last_name' => $lastName,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Cập nhật thông tin thành công.',
                'data' => [
                    'name' => $user->name,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Profile update failed: ' . $e->getMessage(), [
                'user_id' => auth('member')->id(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật thông tin. Vui lòng thử lại sau.'
            ], 500);
        }
    }

    /**
     * Delete user account
     */
    public function deleteAccount(Request $request)
    {
        try {
            $user = auth('member')->user();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Người dùng chưa đăng nhập.'
                ], 401);
            }

            // Log the account deletion
            Log::info('User account deleted', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_name' => $user->name,
            ]);

            // Delete the user account
            $user->delete();

            // Logout the user
            auth('member')->logout();

            return response()->json([
                'success' => true,
                'message' => 'Tài khoản đã được xóa thành công.'
            ]);

        } catch (\Exception $e) {
            Log::error('Account deletion failed: ' . $e->getMessage(), [
                'user_id' => auth('member')->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa tài khoản. Vui lòng thử lại sau.'
            ], 500);
        }
    }

    // ... (rest of the methods will be added in the next step)
}
