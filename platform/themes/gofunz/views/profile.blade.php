{{-- Profile Page Template --}}
@php
    Theme::layout('default');
    Theme::set('pageTitle', '<PERSON><PERSON> sơ cá nhân');
@endphp

<div class="wishlist-bg" style="background-image: url({{ Theme::asset()->url('images/profile_bg.jpg') }});">
</div>

<main class="profile-page">
    @include(Theme::getThemeNamespace('partials.components.search-form'), [
        'isMini' => true,
        'demo' => false
    ])

    <div class="container sm">
        {{-- Profile Header --}}
        <section>
            <div class="profile">
                <img src="{{ auth('member')->user()->avatar_url }}"
                    alt="{{ auth('member')->user()->name }}">
                <h3>{{ auth('member')->user()->name }}</h3>
            </div>
        </section>

        {{-- Personal Information Form --}}
        <div class="persional-form">
            <h3>Thông tin cá nhân</h3>
            <form id="profile-form" method="POST" action="{{ route('public.profile.update') }}">
                @csrf
                @method('PUT')

                {{-- Name Field --}}
                <label>Họ tên</label>
                <div class="group">
                    <input type="text" name="name" value="{{ auth('member')->user()->name }}"
                           class="form-control" required>
                    <button type="submit" class="btn btn-save">Lưu</button>
                </div>

                {{-- Email Field --}}
                <label>Địa chỉ Email</label>
                <input type="email" name="email" value="{{ auth('member')->user()->email }}"
                       class="form-control" readonly>
            </form>

            {{-- Action Buttons --}}
            <div class="buttons">
                <button type="button" class="btn btn-delete" onclick="confirmDeleteAccount()">
                    Xóa tài khoản
                </button>
                <form method="POST" action="{{ route('public.member.logout') }}" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-logout">
                        <i class="fa-light fa-person-to-door"></i> Đăng xuất
                    </button>
                </form>
            </div>
        </div>
    </div>
</main>

{{-- Include mobile menu --}}
{!! Theme::partial('components.mobile-menu') !!}

@push('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle profile form submission via AJAX
    const profileForm = document.getElementById('profile-form');
    if (profileForm) {
        profileForm.addEventListener('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('.btn-save');
            const originalText = submitBtn.textContent;
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

            // Show loading state
            submitBtn.textContent = 'Đang lưu...';
            submitBtn.disabled = true;

            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': csrfToken
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Show success state
                    submitBtn.textContent = 'Đã lưu';
                    submitBtn.style.background = '#28a745';
                    submitBtn.style.color = '#fff';
                    
                    // Show success message if available
                    if (data.message) {
                        showMessage(data.message, 'success');
                    }
                    
                    // Reset button after 2 seconds
                    setTimeout(() => {
                        submitBtn.textContent = originalText;
                        submitBtn.style.background = '';
                        submitBtn.style.color = '';
                        submitBtn.disabled = false;
                    }, 2000);
                } else {
                    throw new Error(data.message || 'Có lỗi xảy ra khi cập nhật thông tin');
                }
            })
            .catch(error => {
                console.error('Profile update error:', error);
                
                // Show error state
                submitBtn.textContent = 'Lỗi';
                submitBtn.style.background = '#dc3545';
                submitBtn.style.color = '#fff';
                
                // Show error message
                showMessage(error.message || 'Có lỗi xảy ra khi cập nhật thông tin. Vui lòng thử lại sau.', 'error');
                
                // Reset button after 3 seconds
                setTimeout(() => {
                    submitBtn.textContent = originalText;
                    submitBtn.style.background = '';
                    submitBtn.style.color = '';
                    submitBtn.disabled = false;
                }, 3000);
            });
        });
    }
    
    // Function to show messages to user
    function showMessage(message, type = 'info') {
        // Remove any existing message
        const existingMessage = document.querySelector('.profile-message');
        if (existingMessage) {
            existingMessage.remove();
        }
        
        // Create new message element
        const messageEl = document.createElement('div');
        messageEl.className = `profile-message alert alert-${type === 'success' ? 'success' : 'danger'}`;
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            background: ${type === 'success' ? '#d4edda' : '#f8d7da'};
            color: ${type === 'success' ? '#155724' : '#721c24'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : '#f5c6cb'};
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            font-size: 14px;
            max-width: 300px;
            word-wrap: break-word;
        `;
        messageEl.textContent = message;
        
        // Add to page
        document.body.appendChild(messageEl);
        
        // Auto remove after 4 seconds
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 4000);
    }
});

function confirmDeleteAccount() {
    if (confirm('Bạn có chắc chắn muốn xóa tài khoản này? Thao tác này không thể hoàn tác.')) {
        fetch('{{ route("public.profile.delete") }}', {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Tài khoản đã được xóa thành công.');
                window.location.href = '/'; // Redirect to home page
            } else {
                alert('Có lỗi xảy ra: ' + (data.message || 'Không thể xóa tài khoản'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi xóa tài khoản. Vui lòng thử lại sau.');
        });
    }
}
</script>
@endpush
