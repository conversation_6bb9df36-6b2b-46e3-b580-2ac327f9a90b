<?php

use Bo<PERSON>ble\Theme\Facades\Theme;
use Illuminate\Support\Facades\Route;
use Theme\Gofunz\Http\Controllers\GofunzController;

// Custom routes
// Theme::routes();

// You can delete this route group if you don't need to add your custom routes.
Route::group(['controller' => GofunzController::class, 'middleware' => ['web', 'core']], function () {
    Route::group(apply_filters(BASE_FILTER_GROUP_PUBLIC_ROUTE, []), function () {

        Route::get('/', 'getIndex')->name('public.index');
        Route::get('place/{slug}', 'getPlace')->name('public.place.detail');
        Route::get('search', 'getSearch')->name('public.search');
        Route::get('blog', 'getListBlog')->name('public.blog.list');
        Route::get('blog/{slug}', 'getDetailBlog')->name('public.single');
        Route::get('map', 'listPriorityPlaces')->name('public.map');
        Route::get('profile', 'getProfile')->name('public.profile')->middleware('member');
        Route::put('profile', 'updateProfile')->name('public.profile.update')->middleware('member');
        Route::delete('profile', 'deleteAccount')->name('public.profile.delete')->middleware('member');
        Route::get('wishlist', 'getWishlist')->name('public.wishlist')->middleware('member');

        Route::prefix('api/v1')->group(function () {
            Route::prefix('places')->group(function () {
                Route::get('latest', 'getListPlace')->name('public.api.places.latest');
                Route::get('search', 'ajaxSearch')->name('public.api.places.search');
                Route::get('category-search', 'categoryAjaxSearch')->name('public.api.places.category-search');
            });

            // For search functionality
            Route::get('search', 'getMoreSearch')->name('public.api.search');
        });
    });
});
